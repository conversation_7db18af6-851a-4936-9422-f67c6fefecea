<script lang="ts">
  import { fetchWithAuth } from "$lib";
  import DetailImageCarousel from "./detail-image-carousel.svelte";

  const i18n = {
    en: {
      _page: {
        title: "— Commune",
      },
      userNotFound: "User not found",
      noDescription: "No description available",
      userDetails: "User Details",
      joinedOn: "Joined on",
      dateFormatLocale: "en-US",
      userNote: "Personal Note",
      userNotePlaceholder: "Write your personal note about this user...",
      saved: "Saved...",
      rating: "Rating",
      karma: "Karma",
      rate: "Rate",
    },
    ru: {
      _page: {
        title: "— Коммуна",
      },
      userNotFound: "Пользователь не найден",
      noDescription: "Нет описания",
      userDetails: "Информация о пользователе",
      joinedOn: "Дата регистрации",
      dateFormatLocale: "ru-RU",
      userNote: "Личная заметка",
      userNotePlaceholder: "Напишите свою личную заметку об этом пользователе...",
      saved: "Сохранено...",
      rating: "Рейтинг",
      karma: "Карма",
      rate: "Оценка",
    },
  };

  const { data } = $props();
  const { user, locale, getAppropriateLocalization, ratingSummary, toLocaleHref } = $derived(data);

  const t = $derived(i18n[locale]);

  // User note state
  let userNote = $state(data.userNote);
  let saveTimeout: ReturnType<typeof setTimeout> | null = $state(null);
  let showSaved = $state(false);

  // Initialize currentNote when userNote is available
  $effect(() => {
    if (userNote !== undefined) {
      userNote = userNote || "";
    }
  });

  // Save handler function (empty for now)
  const saveUserNote = async () => {
    console.log("Saving user note:", userNote);

    const response = await fetchWithAuth(`/api/user/${user.id}/note`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ text: userNote?.trim() || null }),
    });

    if (!response.ok) {
      throw new Error("Failed to save user note");
    }

    // Simulate successful save
    showSaved = true;
    setTimeout(() => {
      showSaved = false;
    }, 2000);
  };

  // Debounced save function
  const debouncedSave = () => {
    if (saveTimeout) {
      clearTimeout(saveTimeout);
    }

    saveTimeout = setTimeout(() => {
      saveUserNote();
    }, 3000);
  };

  // Handle note input changes
  const handleNoteInput = (event: Event) => {
    const target = event.target as HTMLTextAreaElement;
    userNote = target.value;
    debouncedSave();
  };

  // Handle note blur (unfocus)
  const handleNoteBlur = () => {
    if (saveTimeout) {
      clearTimeout(saveTimeout);
      saveTimeout = null;
    }
    saveUserNote();
  };

  // Derived values
  const userName = $derived(getAppropriateLocalization(user.name));
  const userDescription = $derived(getAppropriateLocalization(user.description));

  const joinDate = $derived(user ? new Date(user.createdAt) : new Date());
  const formattedDate = $derived(
    joinDate.toLocaleDateString(t.dateFormatLocale, {
      year: "numeric",
      month: "long",
      day: "numeric",
    }),
  );

  // Get badge class based on user role
  const getBadgeClass = (role: string) => {
    switch (role) {
      case "admin":
        return "bg-danger";
      case "moderator":
        return "bg-warning";
      default:
        return "bg-primary";
    }
  };

  // Get rate color as gradient from dark red (0) to green (10)
  const getRateColor = (rate: number) => {
    // Clamp rate between 0 and 10
    const clampedRate = Math.max(0, Math.min(10, rate));

    // Calculate color components
    // Red component: starts high (139) at 0, decreases to 0 at 10
    const red = Math.round(139 * (1 - clampedRate / 10));

    // Green component: starts low (0) at 0, increases to 128 at 10
    const green = Math.round(128 * (clampedRate / 10));

    // Blue component: stays at 0 for pure red-to-green gradient
    const blue = 0;

    return `rgb(${red}, ${green}, ${blue})`;
  };

  let mockRate = 0;
  let direction = true;

  $inspect(mockRate);

  $effect(() => {
    setInterval(() => {
      console.log("mockRate", mockRate);

      if (direction) mockRate++;
      else mockRate--;

      if (mockRate === 0 || mockRate === 10) direction = !direction;
    }, 500);
  });

  const mockColor = $derived(getRateColor(mockRate));
</script>

<svelte:head>
  <title>{userName} {t._page.title}</title>
</svelte:head>

<div class="container py-4">
  {#if !user}
    <div class="alert alert-danger" role="alert">
      {t.userNotFound}
    </div>
  {:else}
    <div class="row">
      <div class="col-lg-8">
        <!-- Image Carousel -->
        <DetailImageCarousel {locale} images={user.images} />

        <!-- User Information -->
        <div class="mb-4">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h2 class="mb-0">{userName}</h2>
            <span class={`badge ${getBadgeClass(user.role)}`}>
              {user.role}
            </span>
          </div>
          <p class="lead text-muted">{userDescription || t.noDescription}</p>
        </div>
      </div>

      <div class="col-lg-4">
        <div class="card shadow-sm mb-4">
          <div class="card-body">
            <h5 class="card-title">{t.userDetails}</h5>
            <hr />
            <div class="d-flex align-items-center mb-3">
              <i class="bi bi-envelope-fill me-2 text-primary"></i>
              <span>{user.email}</span>
            </div>
            <div class="d-flex align-items-center">
              <i class="bi bi-calendar-date me-2 text-primary"></i>
              <span>{t.joinedOn} {formattedDate}</span>
            </div>
          </div>
        </div>

        <!-- Rating Summary Card -->
        {#if ratingSummary}
          <div class="card shadow-sm mb-4">
            <div class="card-body">
              <h5 class="card-title">User Rating</h5>
              <hr />
              <div class="row g-3">
                <!-- Rating Block -->
                <div class="col-4">
                  <div
                    class="rating-block border rounded p-3 text-center"
                    style="border-color: #fd7e14 !important;"
                  >
                    <div class="rating-label text-muted small mb-1">{t.rating}</div>
                    <div class="rating-value fw-bold" style="color: #fd7e14; font-size: 1.5rem;">
                      {ratingSummary.rating}
                    </div>
                  </div>
                </div>

                <!-- Karma Block -->
                <div class="col-4">
                  <a
                    href={toLocaleHref(`/users/${user.id}/karma`)}
                    class="karma-block border rounded p-3 text-center d-block text-decoration-none"
                    style="border-color: #d63384 !important;"
                  >
                    <div class="karma-label text-muted small mb-1">{t.karma}</div>
                    <div class="karma-value fw-bold" style="color: #d63384; font-size: 1.5rem;">
                      {ratingSummary.karma}
                    </div>
                  </a>
                </div>

                <!-- Rate Block -->
                <div class="col-4">
                  <a
                    href={toLocaleHref(`/users/${user.id}/feedback`)}
                    class="rate-block border rounded p-3 text-center d-block text-decoration-none"
                    style="border-color: #6c757d !important;"
                  >
                    <div class="rate-label text-muted small mb-1">{t.rate}</div>
                    <div
                      class="rate-value fw-bold {ratingSummary.rate === null ? 'text-muted' : ''}"
                      style="font-size: 1.5rem; {ratingSummary.rate !== null
                        ? `color: ${mockColor};`
                        : ''}"
                    >
                      {ratingSummary.rate !== null ? ratingSummary.rate.toFixed(1) : "N/A"}
                    </div>
                  </a>
                </div>
              </div>
            </div>
          </div>
        {/if}

        <!-- User Note Card -->
        <div class="card shadow-sm mb-4">
          <div class="card-body">
            <h5 class="card-title">{t.userNote}</h5>
            <hr />
            <div class="mb-2">
              <textarea
                class="form-control"
                rows="4"
                placeholder={t.userNotePlaceholder}
                value={userNote}
                oninput={handleNoteInput}
                onblur={handleNoteBlur}
              ></textarea>
            </div>
            {#if showSaved}
              <small class="text-muted">{t.saved}</small>
            {/if}
          </div>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .rating-block,
  .karma-block,
  .rate-block {
    transition: all 0.2s ease-in-out;
  }

  .rating-block:hover,
  .karma-block:hover,
  .rate-block:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
</style>
