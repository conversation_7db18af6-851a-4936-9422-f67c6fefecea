<script lang="ts">
  import { fetchWithAuth } from "$lib";
  import DetailImageCarousel from "./detail-image-carousel.svelte";

  const i18n = {
    en: {
      _page: {
        title: "— Commune",
      },
      userNotFound: "User not found",
      noDescription: "No description available",
      userDetails: "User Details",
      joinedOn: "Joined on",
      dateFormatLocale: "en-US",
      userNote: "Personal Note",
      userNotePlaceholder: "Write your personal note about this user...",
      saved: "Saved...",
      rating: "Rating",
      karma: "Karma",
      rate: "Rate",
    },
    ru: {
      _page: {
        title: "— Коммуна",
      },
      userNotFound: "Пользователь не найден",
      noDescription: "Нет описания",
      userDetails: "Информация о пользователе",
      joinedOn: "Дата регистрации",
      dateFormatLocale: "ru-RU",
      userNote: "Личная заметка",
      userNotePlaceholder: "Напишите свою личную заметку об этом пользователе...",
      saved: "Сохранено...",
      rating: "Рейтинг",
      karma: "Карма",
      rate: "Оценка",
    },
  };

  const { data } = $props();
  const { user, locale, getAppropriateLocalization, ratingSummary } = $derived(data);

  const t = $derived(i18n[locale]);

  // User note state
  let userNote = $state(data.userNote);
  let saveTimeout: ReturnType<typeof setTimeout> | null = $state(null);
  let showSaved = $state(false);

  // Rating state
  let showKarmaButtons = $state(false);

  // Initialize currentNote when userNote is available
  $effect(() => {
    if (userNote !== undefined) {
      userNote = userNote || "";
    }
  });

  // Save handler function (empty for now)
  const saveUserNote = async () => {
    console.log("Saving user note:", userNote);

    const response = await fetchWithAuth(`/api/user/${user.id}/note`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ text: userNote?.trim() || null }),
    });

    if (!response.ok) {
      throw new Error("Failed to save user note");
    }

    // Simulate successful save
    showSaved = true;
    setTimeout(() => {
      showSaved = false;
    }, 2000);
  };

  // Debounced save function
  const debouncedSave = () => {
    if (saveTimeout) {
      clearTimeout(saveTimeout);
    }

    saveTimeout = setTimeout(() => {
      saveUserNote();
    }, 3000);
  };

  // Handle note input changes
  const handleNoteInput = (event: Event) => {
    const target = event.target as HTMLTextAreaElement;
    userNote = target.value;
    debouncedSave();
  };

  // Handle note blur (unfocus)
  const handleNoteBlur = () => {
    if (saveTimeout) {
      clearTimeout(saveTimeout);
      saveTimeout = null;
    }
    saveUserNote();
  };

  // Derived values
  const userName = $derived(getAppropriateLocalization(user.name));
  const userDescription = $derived(getAppropriateLocalization(user.description));

  const joinDate = $derived(user ? new Date(user.createdAt) : new Date());
  const formattedDate = $derived(
    joinDate.toLocaleDateString(t.dateFormatLocale, {
      year: "numeric",
      month: "long",
      day: "numeric",
    }),
  );

  // Get badge class based on user role
  const getBadgeClass = (role: string) => {
    switch (role) {
      case "admin":
        return "bg-danger";
      case "moderator":
        return "bg-warning";
      default:
        return "bg-primary";
    }
  };

  // Get rate color class based on value
  const getRateColorClass = (rate: number) => {
    if (rate >= 0 && rate < 2) return "text-danger"; // Dark red
    if (rate >= 2 && rate < 4) return "text-warning"; // Yellow
    if (rate >= 4 && rate <= 5) return "text-success"; // Green
    return "text-muted"; // Default
  };

  // Handle karma button click
  const handleKarmaClick = () => {
    showKarmaButtons = !showKarmaButtons;
  };

  // Handle rate click (placeholder for future functionality)
  const handleRateClick = () => {
    console.log("Rate clicked - future functionality");
  };

  // Handle karma plus/minus (placeholder for future functionality)
  const handleKarmaPlus = () => {
    console.log("Karma plus clicked - future functionality");
  };

  const handleKarmaMinus = () => {
    console.log("Karma minus clicked - future functionality");
  };
</script>

<svelte:head>
  <title>{userName} {t._page.title}</title>
</svelte:head>

<div class="container py-4">
  {#if !user}
    <div class="alert alert-danger" role="alert">
      {t.userNotFound}
    </div>
  {:else}
    <div class="row">
      <div class="col-lg-8">
        <!-- Image Carousel -->
        <DetailImageCarousel {locale} images={user.images} />

        <!-- User Information -->
        <div class="mb-4">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h2 class="mb-0">{userName}</h2>
            <span class={`badge ${getBadgeClass(user.role)}`}>
              {user.role}
            </span>
          </div>
          <p class="lead text-muted">{userDescription || t.noDescription}</p>
        </div>
      </div>

      <div class="col-lg-4">
        <div class="card shadow-sm mb-4">
          <div class="card-body">
            <h5 class="card-title">{t.userDetails}</h5>
            <hr />
            <div class="d-flex align-items-center mb-3">
              <i class="bi bi-envelope-fill me-2 text-primary"></i>
              <span>{user.email}</span>
            </div>
            <div class="d-flex align-items-center">
              <i class="bi bi-calendar-date me-2 text-primary"></i>
              <span>{t.joinedOn} {formattedDate}</span>
            </div>
          </div>
        </div>

        <!-- Rating Summary Card -->
        {#if ratingSummary}
          <div class="card shadow-sm mb-4">
            <div class="card-body">
              <h5 class="card-title">User Rating</h5>
              <hr />
              <div class="row g-3">
                <!-- Rating Block -->
                <div class="col-4">
                  <div
                    class="rating-block border rounded p-3 text-center"
                    style="border-color: #fd7e14 !important;"
                  >
                    <div class="rating-label text-muted small mb-1">{t.rating}</div>
                    <div class="rating-value fw-bold" style="color: #fd7e14; font-size: 1.5rem;">
                      {ratingSummary.rating}
                    </div>
                  </div>
                </div>

                <!-- Karma Block -->
                <div class="col-4">
                  <div
                    class="karma-block border rounded p-3 text-center position-relative"
                    style="border-color: #d63384 !important; cursor: pointer;"
                    onclick={handleKarmaClick}
                    role="button"
                    tabindex="0"
                    onkeydown={(e) => e.key === "Enter" && handleKarmaClick()}
                  >
                    <div class="karma-label text-muted small mb-1">{t.karma}</div>
                    <div class="karma-value fw-bold" style="color: #d63384; font-size: 1.5rem;">
                      {ratingSummary.karma}
                    </div>

                    {#if showKarmaButtons}
                      <div
                        class="karma-buttons position-absolute top-100 start-50 translate-middle-x mt-2 d-flex gap-2"
                      >
                        <button
                          class="btn btn-success btn-sm rounded-circle d-flex align-items-center justify-content-center"
                          style="width: 40px; height: 40px;"
                          onclick={handleKarmaPlus}
                          aria-label="Give karma"
                        >
                          <i class="bi bi-plus-lg"></i>
                        </button>
                        <button
                          class="btn btn-danger btn-sm rounded-circle d-flex align-items-center justify-content-center"
                          style="width: 40px; height: 40px;"
                          onclick={handleKarmaMinus}
                          aria-label="Take karma"
                        >
                          <i class="bi bi-dash-lg"></i>
                        </button>
                      </div>
                    {/if}
                  </div>
                </div>

                <!-- Rate Block -->
                <div class="col-4">
                  <div
                    class="rate-block border rounded p-3 text-center"
                    style="border-color: #6c757d !important; cursor: pointer;"
                    onclick={handleRateClick}
                    role="button"
                    tabindex="0"
                    onkeydown={(e) => e.key === "Enter" && handleRateClick()}
                  >
                    <div class="rate-label text-muted small mb-1">{t.rate}</div>
                    <div
                      class="rate-value fw-bold {ratingSummary.rate !== null
                        ? getRateColorClass(ratingSummary.rate)
                        : 'text-muted'}"
                      style="font-size: 1.5rem;"
                    >
                      {ratingSummary.rate !== null ? ratingSummary.rate.toFixed(1) : "N/A"}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        {/if}

        <!-- User Note Card -->
        <div class="card shadow-sm mb-4">
          <div class="card-body">
            <h5 class="card-title">{t.userNote}</h5>
            <hr />
            <div class="mb-2">
              <textarea
                class="form-control"
                rows="4"
                placeholder={t.userNotePlaceholder}
                value={userNote}
                oninput={handleNoteInput}
                onblur={handleNoteBlur}
              ></textarea>
            </div>
            {#if showSaved}
              <small class="text-muted">{t.saved}</small>
            {/if}
          </div>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .rating-block,
  .karma-block,
  .rate-block {
    transition: all 0.2s ease-in-out;
  }

  .rating-block:hover,
  .karma-block:hover,
  .rate-block:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .karma-buttons {
    z-index: 10;
    animation: fadeInUp 0.3s ease-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translate(-50%, 10px);
    }
    to {
      opacity: 1;
      transform: translate(-50%, 0);
    }
  }

  .karma-buttons button {
    transition: all 0.2s ease-in-out;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .karma-buttons button:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .karma-buttons button:active {
    transform: scale(0.95);
  }
</style>
