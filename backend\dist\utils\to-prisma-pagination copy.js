"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toPrismaPagination = toPrismaPagination;
function toPrismaPagination(data) {
    let pagination = undefined;
    if (data) {
        if ("pagination" in data) {
            pagination = data.pagination;
        }
        else if ("page" in data && "size" in data) {
            pagination = data;
        }
        else if ("limit" in data && "offset" in data) {
            pagination = data;
        }
    }
    if (pagination) {
        if ("page" in pagination && "size" in pagination) {
            const page = pagination.page ?? 1;
            const size = pagination.size ?? 20;
            return {
                skip: (page - 1) * size,
                take: size,
            };
        }
        else if ("limit" in pagination && "offset" in pagination) {
            const limit = pagination.limit ?? 20;
            const offset = pagination.offset ?? 0;
            return {
                skip: offset,
                take: limit,
            };
        }
    }
    return {
        skip: 0,
        take: 20,
    };
}
//# sourceMappingURL=to-prisma-pagination%20copy.js.map