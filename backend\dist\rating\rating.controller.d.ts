import { CurrentUser } from "src/auth/types";
import { RatingService } from "./rating.service";
import { Common, Rating } from "@commune/api";
export declare class RatingController {
    private readonly ratingService;
    constructor(ratingService: RatingService);
    getUserSummary(userId: string): Promise<{
        rating: number;
        karma: number;
        rate: number;
    }>;
    getKarmaPoints(userId: string, pagination: Common.Pagination): Promise<{
        author: {
            id: string;
            email: string;
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            images: {
                id: string;
                url: string;
                createdAt: Date;
                updatedAt: Date;
            }[];
        };
        comment: {
            value: string;
            locale: "en" | "ru";
        }[];
        quantity: number;
    }[]>;
    getUserFeedbacks(userId: string, pagination: Common.Pagination): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        value: number;
        isAnonymous: boolean;
        sourceUserId: string;
        targetUserId: string;
    }[]>;
    spendKarmaPoint(body: Rating.SpendKarmaPointRequest, user: CurrentUser): Promise<boolean>;
    createUserFeedback(body: Rating.CreateUserFeedbackRequest, user: CurrentUser): Promise<void>;
}
