import { CommuneM<PERSON>ber, Prisma } from "@prisma/client";
import { BaseService } from "src/common/base-service";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { MinioService, FileInfo } from "src/minio/minio.service";
import { Common } from "@commune/api";
export declare class CommuneService extends BaseService {
    private readonly prisma;
    private readonly minioService;
    constructor(prisma: PrismaService, minioService: MinioService);
    canGet(id: string, user: CurrentUser): Promise<true>;
    canChange(id: string, user: CurrentUser): Promise<true>;
    check(ids: string[]): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }[]>;
    isHeadMember(commune: {
        members: CommuneMember[];
    }, userId: string): boolean;
    isMember(commune: {
        members: CommuneMember[];
    }, userId: string): boolean;
    getOne(id: string): Promise<({
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        members: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            communeId: string;
            actorType: import("@prisma/client").$Enums.CommuneMemberType;
            actorId: string;
            isHead: boolean;
            joinedAt: Date;
            leftAt: Date | null;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }) | null>;
    getOneOrThrow(id: string): Promise<{
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        members: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            communeId: string;
            actorType: import("@prisma/client").$Enums.CommuneMemberType;
            actorId: string;
            isHead: boolean;
            joinedAt: Date;
            leftAt: Date | null;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    getMany(where: Prisma.CommuneWhereInput, pagination?: {
        page: number;
        size: number;
    }): Promise<({
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        members: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            communeId: string;
            actorType: import("@prisma/client").$Enums.CommuneMemberType;
            actorId: string;
            isHead: boolean;
            joinedAt: Date;
            leftAt: Date | null;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    })[]>;
    createOne(data: Prisma.CommuneCreateInput): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    createMany(data: Prisma.CommuneCreateManyInput[]): Promise<Prisma.BatchPayload>;
    create(data: {
        headUserId?: string;
        name: Common.Localization[];
        description: Common.Localization[];
    }, user: CurrentUser): Promise<{
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        members: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            communeId: string;
            actorType: import("@prisma/client").$Enums.CommuneMemberType;
            actorId: string;
            isHead: boolean;
            joinedAt: Date;
            leftAt: Date | null;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    uploadCommuneImages(communeId: string, files: FileInfo[]): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        url: string;
    }[]>;
    updateOne(id: string, data: Prisma.CommuneUpdateInput): Promise<{
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        members: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            communeId: string;
            actorType: import("@prisma/client").$Enums.CommuneMemberType;
            actorId: string;
            isHead: boolean;
            joinedAt: Date;
            leftAt: Date | null;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    getHeadMember(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        communeId: string;
        actorType: import("@prisma/client").$Enums.CommuneMemberType;
        actorId: string;
        isHead: boolean;
        joinedAt: Date;
        leftAt: Date | null;
    }>;
    update(id: string, data: Prisma.CommuneUpdateInput, user: CurrentUser): Promise<{
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        members: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            communeId: string;
            actorType: import("@prisma/client").$Enums.CommuneMemberType;
            actorId: string;
            isHead: boolean;
            joinedAt: Date;
            leftAt: Date | null;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    updateMany(where: Prisma.CommuneWhereInput, data: Prisma.CommuneUpdateInput): Promise<Prisma.BatchPayload>;
    softDeleteOne(id: string): Promise<{
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        members: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            communeId: string;
            actorType: import("@prisma/client").$Enums.CommuneMemberType;
            actorId: string;
            isHead: boolean;
            joinedAt: Date;
            leftAt: Date | null;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    softDeleteOneCascade(id: string, user: CurrentUser): Promise<void>;
    softDeleteMany(where: Prisma.CommuneWhereInput): Promise<Prisma.BatchPayload>;
    deleteOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    deleteMany(where: Prisma.CommuneWhereInput): Promise<Prisma.BatchPayload>;
    getInvitationsForUser(userId: string, pagination?: {
        page: number;
        size: number;
    }): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        status: import("@prisma/client").$Enums.CommuneInvitationStatus;
        communeId: string;
        expiresAt: Date;
        userId: string;
    }[]>;
    getInvitationsForCommune(communeId: string, pagination?: {
        page: number;
        size: number;
    }): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        status: import("@prisma/client").$Enums.CommuneInvitationStatus;
        communeId: string;
        expiresAt: Date;
        userId: string;
    }[]>;
    createInvitation(communeId: string, userId: string, currentUser: CurrentUser): Promise<{
        id: string;
    }>;
    deleteInvitation(id: string, currentUser: CurrentUser): Promise<boolean>;
    acceptInvitation(id: string, currentUser: CurrentUser): Promise<boolean>;
    rejectInvitation(id: string, currentUser: CurrentUser): Promise<boolean>;
    getJoinRequestsForUser(userId: string, pagination?: {
        page: number;
        size: number;
    }): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        status: import("@prisma/client").$Enums.CommuneJoinRequestStatus;
        communeId: string;
        userId: string;
    }[]>;
    getJoinRequestsForCommune(communeId: string, pagination?: {
        page: number;
        size: number;
    }): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        status: import("@prisma/client").$Enums.CommuneJoinRequestStatus;
        communeId: string;
        userId: string;
    }[]>;
    createJoinRequest(communeId: string, userId: string, currentUser: CurrentUser): Promise<{
        id: string;
    }>;
    deleteJoinRequest(id: string, currentUser: CurrentUser): Promise<boolean>;
    acceptJoinRequest(id: string, currentUser: CurrentUser): Promise<boolean>;
    rejectJoinRequest(id: string, currentUser: CurrentUser): Promise<boolean>;
    transferHeadStatus(communeId: string, newHeadUserId: string, currentUser: CurrentUser): Promise<boolean>;
}
