import type { Common } from "@commune/api";
import type { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
export declare class RatingService {
    private readonly prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    getUserSummary(userId: string): Promise<{
        rating: number;
        karma: number;
        rate: number;
    }>;
    getKarmaPoints(userId: string, pagination?: Common.Pagination): Promise<({
        comment: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        quantity: number;
        sourceUserId: string;
        targetUserId: string;
    })[]>;
    spendKarmaPoint(data: {
        sourceUserId: string;
        targetUserId: string;
        quantity: number;
        comment: Common.Localizations;
    }, user: CurrentUser): Promise<boolean>;
    getUserFeedbacks(userId: string, pagination?: Common.Pagination): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        value: number;
        isAnonymous: boolean;
        sourceUserId: string;
        targetUserId: string;
    }[]>;
    createUserFeedback(data: {
        sourceUserId: string;
        targetUserId: string;
        value: number;
        isAnonymous: boolean;
        text: Common.Localizations;
    }, user: CurrentUser): Promise<void>;
}
