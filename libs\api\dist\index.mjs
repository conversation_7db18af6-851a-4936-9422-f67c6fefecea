var __defProp = Object.defineProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};

// src/common.ts
var common_exports = {};
__export(common_exports, {
  FormDataToObject: () => FormDataToObject,
  ImageSchema: () => ImageSchema,
  ImagesSchema: () => ImagesSchema,
  JsonStringToObject: () => JsonStringToObject,
  LocalizationLocaleSchema: () => LocalizationLocaleSchema,
  LocalizationLocalesSchema: () => LocalizationLocalesSchema,
  LocalizationSchema: () => LocalizationSchema,
  LocalizationsSchema: () => LocalizationsSchema,
  ObjectWithIdSchema: () => ObjectWithIdSchema,
  PaginationSchema: () => PaginationSchema,
  WebsiteLocaleSchema: () => WebsiteLocaleSchema,
  email: () => email,
  id: () => id,
  pagination: () => pagination,
  parseInput: () => parseInput,
  parseUnknown: () => parseUnknown,
  stringToDate: () => stringToDate
});
import { z } from "zod";
var id = z.string().nanoid();
var email = z.string().email();
var stringToDate = z.union([z.number(), z.string(), z.date()]).pipe(z.coerce.date());
function JsonStringToObject(schema) {
  return z.string().transform((value) => JSON.parse(value)).pipe(z.object(schema));
}
function FormDataToObject(schema) {
  return z.object({
    data: JsonStringToObject(schema)
  });
}
var ObjectWithIdSchema = z.object({ id });
var WebsiteLocaleSchema = z.enum(["en", "ru"]);
var LocalizationLocaleSchema = z.enum(["en", "ru"]);
var LocalizationLocalesSchema = z.array(LocalizationLocaleSchema).min(1);
var LocalizationSchema = z.object({
  locale: LocalizationLocaleSchema,
  value: z.string().nonempty()
});
var LocalizationsSchema = z.array(LocalizationSchema);
var ImageSchema = z.object({
  id,
  url: z.string(),
  createdAt: stringToDate,
  updatedAt: stringToDate
});
var ImagesSchema = z.array(ImageSchema);
var pagination = {
  offset: z.coerce.number().int().default(0),
  limit: z.coerce.number().int().positive().max(100).default(20),
  page: z.coerce.number().int().positive().default(1),
  size: z.coerce.number().int().positive().max(100).default(20)
};
var PaginationSchema = z.object({
  page: pagination.page,
  size: pagination.size
});
function parseInput(schema, value) {
  return schema.parse(value);
}
function parseUnknown(schema, value) {
  return schema.parse(value);
}

// src/auth.ts
var auth_exports = {};
__export(auth_exports, {
  GetMeResponseSchema: () => GetMeResponseSchema,
  LoginRequestSchema: () => LoginRequestSchema,
  RegisterRequestSchema: () => RegisterRequestSchema,
  SendOtpRequestSchema: () => SendOtpRequestSchema,
  SendOtpResponseSchema: () => SendOtpResponseSchema,
  SuccessfulAuthResponseSchema: () => SuccessfulAuthResponseSchema,
  otp: () => otp
});
import { z as z3 } from "zod";

// src/user.ts
var user_exports = {};
__export(user_exports, {
  AuthorSchema: () => AuthorSchema,
  GetUserNoteResponseSchema: () => GetUserNoteResponseSchema,
  SetUserNoteRequestSchema: () => SetUserNoteRequestSchema,
  UpdateUserRequestSchema: () => UpdateUserRequestSchema,
  UpdateUserTitleRequestSchema: () => UpdateUserTitleRequestSchema,
  UserRoleSchema: () => UserRoleSchema,
  UserSchema: () => UserSchema,
  UserTitleSchema: () => UserTitleSchema,
  UserTitlesSchema: () => UserTitlesSchema,
  UsersSchema: () => UsersSchema,
  userDescription: () => userDescription,
  userName: () => userName,
  userNoteText: () => userNoteText,
  userTitleColor: () => userTitleColor,
  userTitleIsActive: () => userTitleIsActive
});
import { z as z2 } from "zod";
var userName = LocalizationsSchema;
var userDescription = LocalizationsSchema;
var userTitleIsActive = z2.boolean();
var userTitleColor = z2.string().nonempty().nullable();
var userNoteText = z2.string().nonempty();
var UserRoleSchema = z2.enum([
  "admin",
  "moderator",
  "user"
]);
var AuthorSchema = z2.object({
  id,
  email,
  name: userName,
  images: ImagesSchema
});
var UserSchema = z2.object({
  id,
  email,
  role: UserRoleSchema,
  name: userName,
  description: userDescription,
  images: ImagesSchema,
  createdAt: z2.date(),
  updatedAt: z2.date()
});
var UsersSchema = z2.array(UserSchema);
var UpdateUserRequestSchema = z2.object({
  name: userName,
  description: userDescription
}).partial();
var UserTitleSchema = z2.object({
  id,
  ownerId: id.nullable(),
  isActive: userTitleIsActive,
  color: userTitleColor,
  createdAt: z2.date(),
  updatedAt: z2.date()
});
var UserTitlesSchema = z2.array(UserTitleSchema);
var UpdateUserTitleRequestSchema = z2.object({
  isActive: userTitleIsActive,
  color: userTitleColor
}).partial();
var GetUserNoteResponseSchema = z2.object({
  text: userNoteText.nullable()
});
var SetUserNoteRequestSchema = z2.object({
  text: userNoteText.nullable()
});

// src/auth.ts
var otp = z3.string().nonempty().length(6);
var SendOtpRequestSchema = z3.object({
  email
});
var SendOtpResponseSchema = z3.object({
  isSent: z3.boolean()
});
var GetMeResponseSchema = z3.object({
  id,
  email,
  role: UserRoleSchema,
  name: userName,
  description: userDescription,
  images: ImagesSchema,
  createdAt: z3.date(),
  updatedAt: z3.date()
});
var RegisterRequestSchema = z3.object({
  referrerId: id.nullable(),
  email,
  otp
});
var LoginRequestSchema = z3.object({
  email,
  otp
});
var SuccessfulAuthResponseSchema = z3.object({
  id,
  email,
  role: UserRoleSchema
});

// src/commune.ts
var commune_exports = {};
__export(commune_exports, {
  CommuneInvitationSchema: () => CommuneInvitationSchema,
  CommuneInvitationStatusSchema: () => CommuneInvitationStatusSchema,
  CommuneInvitationsSchema: () => CommuneInvitationsSchema,
  CommuneJoinRequestSchema: () => CommuneJoinRequestSchema,
  CommuneJoinRequestStatusSchema: () => CommuneJoinRequestStatusSchema,
  CommuneJoinRequestsSchema: () => CommuneJoinRequestsSchema,
  CommuneMemberSchema: () => CommuneMemberSchema,
  CommuneMemberTypeSchema: () => CommuneMemberTypeSchema,
  CommuneMembersSchema: () => CommuneMembersSchema,
  CommuneSchema: () => CommuneSchema,
  CommunesSchema: () => CommunesSchema,
  CreateCommuneInvitationRequestSchema: () => CreateCommuneInvitationRequestSchema,
  CreateCommuneJoinRequestRequestSchema: () => CreateCommuneJoinRequestRequestSchema,
  CreateCommuneMemberRequestSchema: () => CreateCommuneMemberRequestSchema,
  CreateCommuneRequestSchema: () => CreateCommuneRequestSchema,
  TransferHeadStatusRequestSchema: () => TransferHeadStatusRequestSchema,
  UpdateCommuneRequestSchema: () => UpdateCommuneRequestSchema,
  communeDescription: () => communeDescription,
  communeMemberActorType: () => communeMemberActorType,
  communeMemberName: () => communeMemberName,
  communeName: () => communeName
});
import { z as z4 } from "zod";
var CommuneMemberTypeSchema = z4.enum(["user"]);
var communeMemberActorType = CommuneMemberTypeSchema;
var communeMemberName = LocalizationsSchema;
var CommuneMemberSchema = z4.object({
  id,
  actorType: communeMemberActorType,
  actorId: id,
  name: communeMemberName,
  images: ImagesSchema,
  joinedAt: z4.date(),
  leftAt: z4.date().nullable()
});
var CommuneMembersSchema = z4.array(CommuneMemberSchema);
var communeName = LocalizationsSchema;
var communeDescription = LocalizationsSchema;
var CommuneSchema = z4.object({
  id,
  name: LocalizationsSchema,
  description: LocalizationsSchema,
  headMember: z4.object({
    actorType: communeMemberActorType,
    actorId: id,
    name: communeMemberName
  }),
  memberCount: z4.number().int().positive(),
  images: ImagesSchema,
  createdAt: z4.date(),
  updatedAt: z4.date()
});
var CommunesSchema = z4.array(CommuneSchema);
var CreateCommuneRequestSchema = JsonStringToObject({
  headUserId: id.optional(),
  name: communeName,
  description: communeDescription
  // Images are handled separately via file upload
});
var UpdateCommuneRequestSchema = z4.object({
  name: communeName,
  description: communeDescription
});
var CreateCommuneMemberRequestSchema = z4.object({
  userId: id
});
var CommuneInvitationStatusSchema = z4.enum(["pending", "accepted", "rejected", "expired"]);
var CommuneInvitationSchema = z4.object({
  id,
  communeId: id,
  userId: id,
  status: CommuneInvitationStatusSchema,
  createdAt: z4.date(),
  updatedAt: z4.date()
});
var CommuneInvitationsSchema = z4.array(CommuneInvitationSchema);
var CreateCommuneInvitationRequestSchema = z4.object({
  communeId: id,
  userId: id
});
var CommuneJoinRequestStatusSchema = z4.enum(["pending", "accepted", "rejected"]);
var CommuneJoinRequestSchema = z4.object({
  id,
  communeId: id,
  userId: id,
  status: CommuneJoinRequestStatusSchema,
  createdAt: z4.date(),
  updatedAt: z4.date()
});
var CommuneJoinRequestsSchema = z4.array(CommuneJoinRequestSchema);
var CreateCommuneJoinRequestRequestSchema = z4.object({
  communeId: id,
  userId: id
});
var TransferHeadStatusRequestSchema = z4.object({
  newHeadUserId: id
});

// src/reactor.ts
var reactor_exports = {};
__export(reactor_exports, {
  AnonimifyCommentRequestSchema: () => AnonimifyCommentRequestSchema,
  AuthorSchema: () => AuthorSchema2,
  CommentEntityTypeSchema: () => CommentEntityTypeSchema,
  CommentSchema: () => CommentSchema,
  CreateCommentRequestSchema: () => CreateCommentRequestSchema,
  CreateLensRequestSchema: () => CreateLensRequestSchema,
  CreatePostRequestSchema: () => CreatePostRequestSchema,
  DeleteCommentRequestSchema: () => DeleteCommentRequestSchema,
  DeletePostRequestSchema: () => DeletePostRequestSchema,
  GetCommentsRequestSchema: () => GetCommentsRequestSchema,
  GetCommentsResponseSchema: () => GetCommentsResponseSchema,
  GetPostsRequestSchema: () => GetPostsRequestSchema,
  GetPostsResponseSchema: () => GetPostsResponseSchema,
  PostSchema: () => PostSchema,
  PostUsefulnessSchema: () => PostUsefulnessSchema,
  RatingSchema: () => RatingSchema,
  RatingTypeSchema: () => RatingTypeSchema,
  UpdateCommentRatingRequestSchema: () => UpdateCommentRatingRequestSchema,
  UpdateCommentRatingResponseSchema: () => UpdateCommentRatingResponseSchema,
  UpdateCommentRequestSchema: () => UpdateCommentRequestSchema,
  UpdateLensRequestSchema: () => UpdateLensRequestSchema,
  UpdatePostRatingRequestSchema: () => UpdatePostRatingRequestSchema,
  UpdatePostRatingResponseSchema: () => UpdatePostRatingResponseSchema,
  UpdatePostRequestSchema: () => UpdatePostRequestSchema,
  UpdatePostUsefulnessRequestSchema: () => UpdatePostUsefulnessRequestSchema,
  UpdatePostUsefulnessResponseSchema: () => UpdatePostUsefulnessResponseSchema,
  commentBody: () => commentBody,
  postBody: () => postBody,
  postTags: () => postTags,
  postTitle: () => postTitle,
  postUsefulness: () => postUsefulness
});
import { z as z5 } from "zod";
var postUsefulness = z5.number().int().min(0).max(10);
var AuthorSchema2 = z5.object({
  id,
  name: userName,
  avatar: z5.string().nullable()
});
var RatingTypeSchema = z5.enum(["like", "dislike"]);
var RatingSchema = z5.object({
  likes: z5.number().int().nonnegative(),
  dislikes: z5.number().int().nonnegative(),
  status: RatingTypeSchema.nullable()
});
var PostUsefulnessSchema = z5.object({
  value: postUsefulness.nullable(),
  count: z5.number().int().nonnegative(),
  totalValue: z5.number().min(0).max(10).nullable()
});
var postTitle = LocalizationsSchema.min(1);
var postBody = LocalizationsSchema.min(1);
var postTags = z5.array(id);
var PostSchema = z5.object({
  id,
  author: AuthorSchema2,
  rating: RatingSchema,
  usefulness: PostUsefulnessSchema,
  title: postTitle,
  body: postBody,
  tags: postTags,
  createdAt: z5.date(),
  updatedAt: z5.date()
});
var GetPostsRequestSchema = z5.object({
  paginationSchema: PaginationSchema
});
var GetPostsResponseSchema = z5.object({
  items: z5.array(PostSchema),
  total: z5.number().int().nonnegative()
});
var CreatePostRequestSchema = z5.object({
  title: postTitle,
  body: postBody,
  tags: postTags
});
var UpdatePostRequestSchema = z5.object({
  title: postTitle,
  body: postBody,
  tags: postTags
}).partial();
var DeletePostRequestSchema = z5.object({
  reason: z5.string().nonempty().nullable()
});
var UpdatePostRatingRequestSchema = z5.object({
  type: RatingTypeSchema
});
var UpdatePostRatingResponseSchema = RatingSchema;
var UpdatePostUsefulnessRequestSchema = z5.object({
  value: postUsefulness.nullable()
});
var UpdatePostUsefulnessResponseSchema = PostUsefulnessSchema;
var CommentEntityTypeSchema = z5.enum(["post", "comment"]);
var commentBody = LocalizationsSchema.min(1);
var CommentSchema = z5.object({
  id,
  path: z5.string().nonempty(),
  author: AuthorSchema2.nullable(),
  isAnonymous: z5.boolean(),
  anonimityReason: z5.string().nonempty().nullable(),
  rating: RatingSchema,
  body: commentBody.nullable(),
  childrenCount: z5.number().int().nonnegative(),
  createdAt: z5.date(),
  updatedAt: z5.date(),
  deletedAt: z5.date().nullable(),
  deleteReason: z5.string().nonempty().nullable()
});
var GetCommentsRequestSchema = z5.object({
  entityType: CommentEntityTypeSchema,
  entityId: id
});
var GetCommentsResponseSchema = z5.object({
  items: z5.array(CommentSchema),
  total: z5.number().int().nonnegative()
});
var CreateCommentRequestSchema = z5.object({
  entityType: CommentEntityTypeSchema,
  entityId: id,
  body: commentBody
});
var UpdateCommentRequestSchema = z5.object({
  body: commentBody
});
var DeleteCommentRequestSchema = z5.object({
  reason: z5.string().nonempty().nullable()
});
var UpdateCommentRatingRequestSchema = z5.object({
  type: RatingTypeSchema
});
var UpdateCommentRatingResponseSchema = RatingSchema;
var AnonimifyCommentRequestSchema = z5.object({
  reason: z5.string().nonempty().nullable()
});
var CreateLensRequestSchema = z5.object({
  name: z5.string().nonempty(),
  code: z5.string().nonempty()
});
var UpdateLensRequestSchema = z5.object({
  name: z5.string().nonempty(),
  code: z5.string().nonempty()
}).partial();

// src/rating.ts
var rating_exports = {};
__export(rating_exports, {
  CreateUserFeedbackRequestSchema: () => CreateUserFeedbackRequestSchema,
  GetKarmaPointsResponseSchema: () => GetKarmaPointsResponseSchema,
  GetUserFeedbacksResponseSchema: () => GetUserFeedbacksResponseSchema,
  GetUserSummaryResponseSchema: () => GetUserSummaryResponseSchema,
  SpendKarmaPointRequestSchema: () => SpendKarmaPointRequestSchema,
  karmaPointComment: () => karmaPointComment,
  karmaPointQuantity: () => karmaPointQuantity,
  userFeedbackText: () => userFeedbackText,
  userFeedbackValue: () => userFeedbackValue
});
import { z as z6 } from "zod";
var karmaPointQuantity = z6.number().int();
var karmaPointComment = LocalizationsSchema.min(1);
var GetKarmaPointsResponseSchema = z6.array(
  z6.object({
    author: AuthorSchema,
    quantity: karmaPointQuantity,
    comment: karmaPointComment
  })
);
var SpendKarmaPointRequestSchema = z6.object({
  sourceUserId: id,
  targetUserId: id,
  quantity: karmaPointQuantity,
  comment: karmaPointComment
});
var userFeedbackValue = z6.number().int().min(0).max(10);
var userFeedbackText = LocalizationsSchema.min(1);
var GetUserFeedbacksResponseSchema = z6.array(
  z6.object({
    author: AuthorSchema.nullable(),
    isAnonymous: z6.boolean(),
    value: userFeedbackValue,
    text: userFeedbackText
  })
);
var CreateUserFeedbackRequestSchema = z6.object({
  sourceUserId: id,
  targetUserId: id,
  value: userFeedbackValue,
  isAnonymous: z6.boolean(),
  text: userFeedbackText
});
var GetUserSummaryResponseSchema = z6.object({
  rating: z6.number().int(),
  karma: z6.number().int(),
  rate: userFeedbackValue
});
export {
  auth_exports as Auth,
  common_exports as Common,
  commune_exports as Commune,
  rating_exports as Rating,
  reactor_exports as Reactor,
  user_exports as User
};
//# sourceMappingURL=index.mjs.map