<script lang="ts">
  import type { Rating } from "@commune/api";
  import { fetchWithAuth } from "$lib";

  const i18n = {
    en: {
      _page: {
        title: "Karma — Commune",
      },
      userNotFound: "User not found",
      karmaHistory: "Karma History",
      giveKarma: "Give Karma",
      takeKarma: "Take Karma",
      noKarmaChanges: "No karma changes found",
      loadingMore: "Loading more...",
      errorOccurred: "An error occurred",
      errorFetchingKarma: "Error fetching karma changes",
    },
    ru: {
      _page: {
        title: "Карма — Коммуна",
      },
      userNotFound: "Пользователь не найден",
      karmaHistory: "История кармы",
      giveKarma: "Дать карму",
      takeKarma: "Забрать карму",
      noKarmaChanges: "Изменения кармы не найдены",
      loadingMore: "Загрузка...",
      errorOccurred: "Произошла ошибка",
      errorFetchingKarma: "Ошибка загрузки изменений кармы",
    },
  };

  const { data } = $props();
  const { user, locale, getAppropriateLocalization } = $derived(data);

  const t = $derived(i18n[locale]);

  // State using runes
  let karmaPoints = $state(data.karmaPoints);
  let error = $state<string | null>(null);

  const PAGE_SIZE = 20;

  let isLoadingMore = $state(false);
  let currentPage = $state(1);
  let isHasMoreKarma = $state(data.isHasMoreKarma);

  // Reference to the sentinel element for intersection observer
  let sentinelElement = $state<HTMLElement | null>(null);

  // Function to load more karma points
  async function loadMoreKarmaPoints() {
    if (isLoadingMore || !isHasMoreKarma) return;

    isLoadingMore = true;
    error = null;

    try {
      const nextPage = currentPage + 1;
      const response = await fetchWithAuth(
        `/api/rating/${user.id}/karma?page=${nextPage}&size=${PAGE_SIZE}`,
      );

      if (!response.ok) {
        throw new Error(`${t.errorFetchingKarma}: ${response.statusText}`);
      }

      const newKarmaPoints: Rating.GetKarmaPointsResponse = await response.json();

      // Append new karma points to existing list
      karmaPoints = [...karmaPoints, ...newKarmaPoints];
      currentPage = nextPage;

      // Check if there are more karma points to load
      isHasMoreKarma = newKarmaPoints.length === PAGE_SIZE;
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isLoadingMore = false;
    }
  }

  // Set up intersection observer for infinite scroll
  $effect(() => {
    if (!sentinelElement) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && isHasMoreKarma && !isLoadingMore) {
          loadMoreKarmaPoints();
        }
      },
      { threshold: 0.1 },
    );

    observer.observe(sentinelElement);

    return () => {
      observer.disconnect();
    };
  });

  // Derived values
  const userName = $derived(getAppropriateLocalization(user.name));

  // Handle karma actions (placeholder for future functionality)
  const handleGiveKarma = () => {
    console.log("Give karma clicked - future functionality");
  };

  const handleTakeKarma = () => {
    console.log("Take karma clicked - future functionality");
  };

  // Get author display name
  const getAuthorDisplayName = (author: Rating.GetKarmaPointsResponse[0]["author"]) => {
    const name = getAppropriateLocalization(author.name);
    return name || author.email;
  };

  // Get author avatar URL
  const getAuthorAvatar = (author: Rating.GetKarmaPointsResponse[0]["author"]) => {
    return author.images && author.images.length > 0 ? author.images[0].url : null;
  };

  // Format quantity with sign
  const formatQuantity = (quantity: number) => {
    return quantity > 0 ? `+${quantity}` : `${quantity}`;
  };

  // Get quantity color class
  const getQuantityColorClass = (quantity: number) => {
    return quantity > 0 ? "text-success" : "text-danger";
  };
</script>

<svelte:head>
  <title>{userName} {t._page.title}</title>
</svelte:head>

<div class="container py-4">
  {#if !user}
    <div class="alert alert-danger" role="alert">
      {t.userNotFound}
    </div>
  {:else}
    <!-- Header with user name and action buttons -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <h2 class="mb-1">{userName}</h2>
        <p class="text-muted mb-0">{t.karmaHistory}</p>
      </div>

      <!-- Action buttons in top right corner -->
      <div class="d-flex gap-2">
        <button class="btn btn-success btn-sm" onclick={handleGiveKarma} aria-label={t.giveKarma}>
          <i class="bi bi-plus-lg me-1"></i>
          {t.giveKarma}
        </button>
        <button class="btn btn-danger btn-sm" onclick={handleTakeKarma} aria-label={t.takeKarma}>
          <i class="bi bi-dash-lg me-1"></i>
          {t.takeKarma}
        </button>
      </div>
    </div>

    <!-- Karma changes list -->
    {#if karmaPoints.length === 0}
      <div class="text-center py-5">
        <p class="text-muted">{t.noKarmaChanges}</p>
      </div>
    {:else}
      <div class="row">
        <div class="col-lg-8 mx-auto">
          {#each karmaPoints as karmaPoint (karmaPoint.author.id + karmaPoint.quantity + karmaPoint.comment)}
            <div class="card mb-3 shadow-sm">
              <div class="card-body">
                <div class="d-flex align-items-start">
                  <!-- Author avatar -->
                  <div class="me-3">
                    {#if getAuthorAvatar(karmaPoint.author)}
                      <img
                        src={getAuthorAvatar(karmaPoint.author)}
                        alt={getAuthorDisplayName(karmaPoint.author)}
                        class="rounded-circle"
                        style="width: 48px; height: 48px; object-fit: cover;"
                      />
                    {:else}
                      <div
                        class="rounded-circle bg-secondary d-flex align-items-center justify-content-center text-white"
                        style="width: 48px; height: 48px;"
                      >
                        <i class="bi bi-person-fill"></i>
                      </div>
                    {/if}
                  </div>

                  <!-- Content -->
                  <div class="flex-grow-1">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                      <div>
                        <h6 class="mb-1">{getAuthorDisplayName(karmaPoint.author)}</h6>
                      </div>
                      <span class="badge {getQuantityColorClass(karmaPoint.quantity)} fs-6">
                        {formatQuantity(karmaPoint.quantity)}
                      </span>
                    </div>

                    <!-- Comment -->
                    <p class="mb-0 text-muted">
                      {getAppropriateLocalization(karmaPoint.comment)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          {/each}
        </div>
      </div>
    {/if}

    <!-- Infinite scroll sentinel element -->
    {#if isHasMoreKarma}
      <div bind:this={sentinelElement} class="text-center py-3">
        {#if isLoadingMore}
          <div class="spinner-border spinner-border-sm" role="status">
            <span class="visually-hidden">{t.loadingMore}</span>
          </div>
          <p class="text-muted mt-2 mb-0">{t.loadingMore}</p>
        {/if}
      </div>
    {/if}

    {#if error}
      <div class="alert alert-danger" role="alert">
        {error}
      </div>
    {/if}
  {/if}
</div>

<style>
  .card {
    transition: transform 0.2s ease-in-out;
  }

  .card:hover {
    transform: translateY(-2px);
  }

  .badge {
    font-weight: bold;
  }
</style>
