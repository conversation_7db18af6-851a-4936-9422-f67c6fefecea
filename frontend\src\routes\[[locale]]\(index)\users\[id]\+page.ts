import type { PageLoad } from "./$types";
import type { User } from "@commune/api";

import { error } from "@sveltejs/kit";
import { fixResponseJsonDates, handleUnauthorized } from "$lib";

export const load: PageLoad = async ({ fetch, params, url }) => {
  const [
    userResponse,
    userNoteResponse,
  ] = await Promise.all([
    fetch(`/api/user/${params.id}`),
    fetch(`/api/user/${params.id}/note`),
  ])

  handleUnauthorized(userResponse, url);
  handleUnauthorized(userNoteResponse, url);

  if (!userResponse.ok) {
    throw error(500, {
      message: `Failed to fetch user: ${userResponse.statusText}`,
    });
  }

  if (!userNoteResponse.ok) {
    throw error(500, {
      message: `Failed to fetch user note: ${userNoteResponse.statusText}`,
    });
  }

  const user: User.User = await userResponse.json().then(fixResponseJsonDates);
  
  const userNote: User.GetUserNoteResponse = await userNoteResponse.json();

  return {
    user,
    userNote: userNote.text,
  };
};
