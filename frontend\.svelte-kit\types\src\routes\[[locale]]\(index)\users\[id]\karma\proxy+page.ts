// @ts-nocheck
import type { PageLoad } from "./$types";
import type { User, Rating, Common } from "@commune/api";

import { error } from "@sveltejs/kit";
import { fixResponseJsonDates, handleUnauthorized } from "$lib";

export const load = async ({ fetch, params, url }: Parameters<PageLoad>[0]) => {
  const [
    userResponse,
    karmaPointsResponse,
  ] = await Promise.all([
    fetch(`/api/user/${params.id}`),
    fetch(`/api/rating/${params.id}/karma?page=1&size=20`),
  ])

  handleUnauthorized(userResponse, url);
  handleUnauthorized(karmaPointsResponse, url);

  if (!userResponse.ok) {
    throw error(500, {
      message: `Failed to fetch user: ${userResponse.statusText}`,
    });
  }

  if (!karmaPointsResponse.ok) {
    throw error(500, {
      message: `Failed to fetch karma points: ${karmaPointsResponse.statusText}`,
    });
  }

  const user: User.User = await userResponse.json().then(fixResponseJsonDates);
  const karmaPoints: Rating.GetKarmaPointsResponse = await karmaPointsResponse.json();

  return {
    user,
    karmaPoints,
    isHasMoreKarma: karmaPoints.length === 20, // If we got a full page, there might be more
  };
};