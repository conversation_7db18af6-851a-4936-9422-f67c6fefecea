"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RatingController = void 0;
const common_1 = require("@nestjs/common");
const zod_1 = require("../zod");
const current_user_decorator_1 = require("../auth/http/current-user.decorator");
const session_auth_guard_1 = require("../auth/http/session-auth.guard");
const rating_service_1 = require("./rating.service");
const api_1 = require("@commune/api");
let RatingController = class RatingController {
    constructor(ratingService) {
        this.ratingService = ratingService;
    }
    async getUserSummary(userId) {
        const summary = await this.ratingService.getUserSummary(userId);
        return api_1.Common.parseInput(api_1.Rating.GetUserSummaryResponseSchema, summary);
    }
    async getKarmaPoints(userId, pagination) {
        const points = await this.ratingService.getKarmaPoints(userId, pagination);
        return api_1.Common.parseInput(api_1.Rating.GetKarmaPointsResponseSchema, points);
    }
    async getUserFeedbacks(userId, pagination) {
        return this.ratingService.getUserFeedbacks(userId, pagination);
    }
    async spendKarmaPoint(body, user) {
        return this.ratingService.spendKarmaPoint(body, user);
    }
    async createUserFeedback(body, user) {
        return this.ratingService.createUserFeedback(body, user);
    }
};
exports.RatingController = RatingController;
__decorate([
    (0, common_1.Get)(":userId/summary"),
    __param(0, (0, common_1.Param)("userId")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], RatingController.prototype, "getUserSummary", null);
__decorate([
    (0, common_1.Get)(":userId/karma"),
    __param(0, (0, common_1.Param)("userId")),
    __param(1, (0, common_1.Query)(new zod_1.ZodPipe(api_1.Common.PaginationSchema))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], RatingController.prototype, "getKarmaPoints", null);
__decorate([
    (0, common_1.Get)(":userId/feedback"),
    __param(0, (0, common_1.Param)("userId")),
    __param(1, (0, common_1.Query)(new zod_1.ZodPipe(api_1.Common.PaginationSchema))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], RatingController.prototype, "getUserFeedbacks", null);
__decorate([
    (0, common_1.Post)("karma"),
    __param(0, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Rating.SpendKarmaPointRequestSchema))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], RatingController.prototype, "spendKarmaPoint", null);
__decorate([
    (0, common_1.Post)("feedback"),
    __param(0, (0, common_1.Body)(new zod_1.ZodPipe(api_1.Rating.CreateUserFeedbackRequestSchema))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], RatingController.prototype, "createUserFeedback", null);
exports.RatingController = RatingController = __decorate([
    (0, common_1.Controller)("rating"),
    (0, common_1.UseGuards)(session_auth_guard_1.HttpSessionAuthGuard),
    __metadata("design:paramtypes", [rating_service_1.RatingService])
], RatingController);
//# sourceMappingURL=rating.controller.js.map