import {
    Body,
    Controller,
    Get,
    Param,
    Post,
    Query,
    UseGuards,
} from "@nestjs/common";
import { ZodPipe } from "src/zod";
import { CurrentUser } from "src/auth/types";
import { HttpCurrentUser } from "src/auth/http/current-user.decorator";
import { HttpSessionAuthGuard } from "src/auth/http/session-auth.guard";
import { RatingService } from "./rating.service";

import { Common, Rating } from "@commune/api";

@Controller("rating")
@UseGuards(HttpSessionAuthGuard)
export class RatingController {
    constructor(private readonly ratingService: RatingService) {}

    @Get(":userId/summary")
    async getUserSummary(@Param("userId") userId: string) {
        const summary = await this.ratingService.getUserSummary(userId);

        return Common.parseInput(Rating.GetUserSummaryResponseSchema, summary);
    }

    @Get(":userId/karma")
    async getKarmaPoints(
        @Param("userId") userId: string,
        @Query(new ZodPipe(Common.PaginationSchema))
        pagination: Common.Pagination,
    ) {
        const points = await this.ratingService.getKarmaPoints(
            userId,
            pagination,
        );

        return Common.parseInput(
            Rating.GetKarmaPointsResponseSchema,
            points.map((p) => ({
                author: p.sourceUser,
                quantity: p.quantity,
                comment: p.comment,
            })),
        );
    }

    @Get(":userId/feedback")
    async getUserFeedbacks(
        @Param("userId") userId: string,
        @Query(new ZodPipe(Common.PaginationSchema))
        pagination: Common.Pagination,
    ) {
        const feedbacks = await this.ratingService.getUserFeedbacks(
            userId,
            pagination,
        );

        return Common.parseInput(
            Rating.GetUserFeedbacksResponseSchema,
            feedbacks.map((f) => ({
                author: f.isAnonymous ? null : f.sourceUser,
                isAnonymous: f.isAnonymous,
                value: f.value,
                text: f.text,
            })),
        );
    }

    @Post("karma")
    async spendKarmaPoint(
        @Body(new ZodPipe(Rating.SpendKarmaPointRequestSchema))
        body: Rating.SpendKarmaPointRequest,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        const result = await this.ratingService.spendKarmaPoint(body, user);

        return Common.parseInput(Common.ObjectWithIdSchema, result);
    }

    @Post("feedback")
    async createUserFeedback(
        @Body(new ZodPipe(Rating.CreateUserFeedbackRequestSchema))
        body: Rating.CreateUserFeedbackRequest,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        const result = await this.ratingService.createUserFeedback(body, user);

        return Common.parseInput(Common.ObjectWithIdSchema, result);
    }
}
